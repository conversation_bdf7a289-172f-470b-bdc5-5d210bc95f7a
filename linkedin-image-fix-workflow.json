{"name": "LinkedIn Image Fix - Production Ready", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * *"}]}}, "id": "scheduler-trigger", "name": "Daily Scheduler", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [100, 100]}, {"parameters": {}, "id": "manual-trigger", "name": "Manual Test", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [100, 200]}, {"parameters": {"jsCode": "// LinkedIn Content Generator\nconst generateLinkedInContent = () => {\n  const content = `🎓 Digital Marketing Insight: The Power of Educational Content\n\nAfter working with 100+ businesses, I've learned that the most successful companies don't just sell - they educate first.\n\nHere's what I've discovered:\n\n✅ Educational content builds trust faster than any sales pitch\n✅ People buy from experts they learn from\n✅ Teaching your expertise creates lasting relationships\n✅ Value-first approaches generate 3x more engagement\n\nReal example: A local bakery started sharing baking tips instead of just posting product photos. Result? 340% increase in engagement and 67% growth in sales.\n\nThe lesson? When you help people succeed, business success follows naturally.\n\nAt GOD Digital Marketing, we specialize in education-first marketing strategies that build genuine relationships.\n\nWhat's one way you could help your customers today without selling anything?\n\nLearn more: https://godigitalmarketing.com\n\n#DigitalMarketingEducation #BusinessGrowth #ValueFirst #GODDigitalMarketing`;\n\n  return {\n    linkedinPost: content,\n    imagePrompt: \"professional business workspace with laptop, charts, and marketing materials, modern office setting, clean design, business success theme, professional lighting, high quality, no text, no watermarks\"\n  };\n};\n\nreturn generateLinkedInContent();"}, "id": "content-generator", "name": "Content Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 150]}, {"parameters": {"method": "POST", "url": "https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer *************************************"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "inputs", "value": "={{ $json.imagePrompt }}"}]}, "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "linkedin_image"}}, "timeout": 60000, "retry": {"enabled": true, "maxAttempts": 3, "waitBetween": 2000}}}, "id": "image-generator", "name": "LinkedIn Image Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [500, 150]}], "connections": {"Daily Scheduler": {"main": [[{"node": "Content Generator", "type": "main", "index": 0}]]}, "Manual Test": {"main": [[{"node": "Content Generator", "type": "main", "index": 0}]]}, "Content Generator": {"main": [[{"node": "LinkedIn Image Generator", "type": "main", "index": 0}]]}}}