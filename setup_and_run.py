#!/usr/bin/env python3
"""
Setup and Run Script for N8N LinkedIn Image Fix
Installs dependencies and runs the workflow manager
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    packages = ["requests"]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def check_n8n_running():
    """Check if N8N is running"""
    print("🔍 Checking if N8N is running...")
    
    try:
        import requests
        response = requests.get("http://localhost:5678/api/v1/workflows", timeout=5)
        if response.status_code in [200, 401]:  # 401 means auth required but n8n is running
            print("✅ N8N is running")
            return True
        else:
            print("❌ N8N is not responding correctly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to N8N: {str(e)}")
        print("\n💡 Please make sure:")
        print("   • N8N is running (npm start or docker)")
        print("   • N8N is accessible on http://localhost:5678")
        print("   • API access is enabled in N8N settings")
        return False

def main():
    """Main setup and run function"""
    print("🚀 N8N LinkedIn Image Fix - Setup & Run")
    print("="*50)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Check N8N
    if not check_n8n_running():
        print("❌ N8N is not running or not accessible")
        sys.exit(1)
    
    # Run the workflow manager
    print("\n🎯 Starting workflow manager...")
    try:
        # Import and run the workflow manager
        from n8n_workflow_manager import main as run_workflow_manager
        run_workflow_manager()
    except ImportError:
        print("❌ Cannot import workflow manager")
        print("💡 Make sure n8n_workflow_manager.py is in the same directory")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error running workflow manager: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
