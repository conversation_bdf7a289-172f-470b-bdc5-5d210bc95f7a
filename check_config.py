#!/usr/bin/env python3
"""
Configuration Checker for N8N LinkedIn Image Fix
Verifies all prerequisites are met before deployment
"""

import requests
import sys
import json

def check_n8n_connection():
    """Check N8N API connection"""
    print("🔍 Checking N8N connection...")
    
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwZWFlOTJmYy05M2RkLTRjMTQtOTRmMC1lODdmY2IzN2MyYmYiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ2NTk5MjQxfQ.jphn46ViMNya7xlk4xrZWY95ad2Bb5P0qnXmLpotBO4"
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get("http://localhost:5678/api/v1/workflows", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ N8N API connection successful")
            workflows = response.json().get('data', [])
            print(f"📋 Found {len(workflows)} existing workflows")
            return True
        else:
            print(f"❌ N8N API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to N8N")
        print("💡 Make sure N8N is running on http://localhost:5678")
        return False
    except Exception as e:
        print(f"❌ Connection error: {str(e)}")
        return False

def check_huggingface_api():
    """Check HuggingFace API"""
    print("\n🔍 Checking HuggingFace API...")
    
    headers = {
        'Authorization': 'Bearer *************************************',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test with a simple request
        response = requests.post(
            "https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev",
            headers=headers,
            json={"inputs": "test"},
            timeout=30
        )
        
        if response.status_code in [200, 503]:  # 503 means model loading
            print("✅ HuggingFace API accessible")
            if response.status_code == 503:
                print("⏳ Model is loading (this is normal)")
            return True
        else:
            print(f"❌ HuggingFace API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ HuggingFace API error: {str(e)}")
        return False

def check_linkedin_credentials():
    """Check LinkedIn credentials setup"""
    print("\n🔍 Checking LinkedIn credentials...")
    
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwZWFlOTJmYy05M2RkLTRjMTQtOTRmMC1lODdmY2IzN2MyYmYiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ2NTk5MjQxfQ.jphn46ViMNya7xlk4xrZWY95ad2Bb5P0qnXmLpotBO4"
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get("http://localhost:5678/api/v1/credentials", headers=headers, timeout=10)
        if response.status_code == 200:
            credentials = response.json().get('data', [])
            linkedin_creds = [c for c in credentials if 'linkedin' in c.get('type', '').lower()]
            
            if linkedin_creds:
                print(f"✅ Found {len(linkedin_creds)} LinkedIn credential(s)")
                for cred in linkedin_creds:
                    print(f"   • {cred.get('name', 'Unnamed')} ({cred.get('type', 'Unknown type')})")
                return True
            else:
                print("⚠️ No LinkedIn credentials found")
                print("💡 You'll need to set up LinkedIn OAuth2 credentials in N8N")
                return False
        else:
            print(f"❌ Cannot check credentials: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking credentials: {str(e)}")
        return False

def print_setup_instructions():
    """Print setup instructions"""
    print("\n" + "="*60)
    print("📋 SETUP INSTRUCTIONS")
    print("="*60)
    
    print("\n🔧 LinkedIn Credentials Setup:")
    print("1. Go to N8N → Credentials")
    print("2. Add new credential → LinkedIn OAuth2 API")
    print("3. Follow LinkedIn OAuth setup process")
    print("4. Note the credential ID for the workflow")
    
    print("\n👤 LinkedIn Person ID:")
    print("1. Go to your LinkedIn profile")
    print("2. Copy the person ID from the URL")
    print("3. Update the workflow with your person ID")
    
    print("\n🚀 Ready to Deploy:")
    print("1. Run: python setup_and_run.py")
    print("2. Or double-click: run_linkedin_fix.bat")
    
    print("\n" + "="*60)

def main():
    """Main configuration check"""
    print("🔧 N8N LinkedIn Image Fix - Configuration Checker")
    print("="*60)
    
    checks = {
        "N8N Connection": check_n8n_connection(),
        "HuggingFace API": check_huggingface_api(),
        "LinkedIn Credentials": check_linkedin_credentials()
    }
    
    print("\n📊 Configuration Summary:")
    print("-" * 30)
    
    all_good = True
    for check_name, result in checks.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name}: {status}")
        if not result:
            all_good = False
    
    print("-" * 30)
    
    if all_good:
        print("\n🎉 All checks passed! Ready to deploy workflow.")
        print("Run: python setup_and_run.py")
    else:
        print("\n⚠️ Some checks failed. Please fix the issues above.")
        print_setup_instructions()

if __name__ == "__main__":
    main()
