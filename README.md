# N8N LinkedIn Image Fix - Automated Deployment & Testing

This script automatically creates, deploys, and tests a LinkedIn image posting workflow in your N8N instance.

## 🚀 Quick Start

### Prerequisites
- N8N running locally on `http://localhost:5678`
- Python 3.6+ installed
- Your N8N API key (already configured in the script)

### Step 1: Run the Setup Script
```bash
python setup_and_run.py
```

This will:
1. ✅ Install required Python packages (`requests`)
2. ✅ Check if N8N is running
3. ✅ Deploy the LinkedIn image fix workflow
4. ✅ Test the workflow automatically
5. ✅ Provide detailed results analysis

### Step 2: Review Results
The script will show you:
- ✅ Whether LinkedIn posting worked
- ✅ Whether image generation succeeded
- ✅ Detailed error analysis if issues occur
- ✅ Complete execution report

## 🔧 What the Script Does

### 1. Workflow Creation
- Creates a complete LinkedIn posting workflow
- Includes image generation using FLUX.1-dev
- Adds comprehensive error handling
- Implements fallback mechanisms

### 2. Image Generation Pipeline
```
Content Generator → Image Generator → Image Validator → LinkedIn Poster → Final Validator
```

### 3. Error Handling
- **Image Generation Fails**: Creates professional fallback image
- **LinkedIn API Issues**: Provides detailed error reporting
- **Network Problems**: Implements retry logic
- **Validation Failures**: Shows exactly what went wrong

### 4. Testing & Analysis
- Executes the workflow automatically
- Waits for completion (up to 3 minutes)
- Analyzes all node results
- Provides comprehensive success/failure report

## 📋 Expected Output

### Success Case:
```
🎉 SUCCESS: LinkedIn image posting is working correctly!
📊 LINKEDIN IMAGE FIX - TEST REPORT
🎯 Overall Success: ✅ YES
📱 LinkedIn Posted: ✅ YES
🖼️ Image Generated: ✅ YES
```

### With Fallback Image:
```
⚠️ Image generation failed, but fallback worked
📊 LINKEDIN IMAGE FIX - TEST REPORT
🎯 Overall Success: ✅ YES
📱 LinkedIn Posted: ✅ YES
🖼️ Image Generated: ❌ NO (fallback used)
```

## 🛠️ Configuration

### LinkedIn Credentials
You'll need to update these in the workflow:
1. **Person ID**: Update `"person": "Kn0HZYImT9"` with your LinkedIn person ID
2. **Credentials**: Set up LinkedIn OAuth2 credentials in N8N

### HuggingFace API
The script uses the HuggingFace API key already in your workflow:
- Token: `*************************************`

### N8N API Access
Make sure API access is enabled in your N8N settings:
1. Go to N8N Settings
2. Enable "API" access
3. Use the provided API key

## 🔍 Troubleshooting

### Common Issues:

#### "Cannot connect to N8N"
- ✅ Check N8N is running: `http://localhost:5678`
- ✅ Verify API access is enabled
- ✅ Check firewall/port settings

#### "LinkedIn posting failed"
- ✅ Verify LinkedIn credentials are set up
- ✅ Check LinkedIn person ID is correct
- ✅ Ensure LinkedIn OAuth2 is properly configured

#### "Image generation failed"
- ✅ Check HuggingFace API key is valid
- ✅ Verify internet connection
- ✅ The script will use fallback images automatically

#### "Workflow execution timeout"
- ✅ Check N8N server performance
- ✅ Verify HuggingFace API is responding
- ✅ Increase timeout in script if needed

## 📁 Files Included

- `n8n_workflow_manager.py` - Main workflow management script
- `setup_and_run.py` - Setup and execution script
- `README.md` - This documentation

## 🎯 Next Steps

After successful testing:
1. ✅ The workflow will be active in your N8N instance
2. ✅ You can schedule it to run automatically
3. ✅ Monitor execution logs in N8N interface
4. ✅ Customize content and image prompts as needed

## 🔒 Security Notes

- ✅ API key is embedded in script (rotate after use)
- ✅ LinkedIn credentials stored in N8N (secure)
- ✅ HuggingFace token included (consider rotating)

## 📞 Support

If you encounter issues:
1. Check the detailed error output from the script
2. Review N8N execution logs
3. Verify all credentials are properly configured
4. Ensure all APIs are accessible

The script provides comprehensive error reporting to help diagnose any issues quickly.
