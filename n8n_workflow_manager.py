#!/usr/bin/env python3
"""
N8N Workflow Manager - Complete Automation Script
Handles workflow creation, deployment, testing, and analysis
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any

class N8NWorkflowManager:
    def __init__(self, api_key: str, base_url: str = "http://localhost:5678"):
        """Initialize N8N API client"""
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
    def test_connection(self) -> bool:
        """Test API connection"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/workflows", headers=self.headers)
            if response.status_code == 200:
                print("✅ N8N API connection successful")
                return True
            else:
                print(f"❌ API connection failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Connection error: {str(e)}")
            return False
    
    def get_workflows(self) -> List[Dict]:
        """Get all workflows"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/workflows", headers=self.headers)
            if response.status_code == 200:
                workflows = response.json()['data']
                print(f"📋 Found {len(workflows)} workflows")
                return workflows
            else:
                print(f"❌ Failed to get workflows: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error getting workflows: {str(e)}")
            return []
    
    def find_workflow_by_name(self, name: str) -> Optional[Dict]:
        """Find workflow by name"""
        workflows = self.get_workflows()
        for workflow in workflows:
            if workflow.get('name') == name:
                return workflow
        return None
    
    def get_workflow_details(self, workflow_id: str) -> Optional[Dict]:
        """Get detailed workflow information"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/workflows/{workflow_id}", headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get workflow details: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error getting workflow details: {str(e)}")
            return None
    
    def update_workflow(self, workflow_id: str, workflow_data: Dict) -> bool:
        """Update existing workflow"""
        try:
            response = requests.put(
                f"{self.base_url}/api/v1/workflows/{workflow_id}",
                headers=self.headers,
                json=workflow_data
            )
            if response.status_code == 200:
                print(f"✅ Workflow {workflow_id} updated successfully")
                return True
            else:
                print(f"❌ Failed to update workflow: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Error updating workflow: {str(e)}")
            return False
    
    def activate_workflow(self, workflow_id: str) -> bool:
        """Activate workflow"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/workflows/{workflow_id}/activate",
                headers=self.headers
            )
            if response.status_code == 200:
                print(f"✅ Workflow {workflow_id} activated")
                return True
            else:
                print(f"❌ Failed to activate workflow: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error activating workflow: {str(e)}")
            return False
    
    def execute_workflow(self, workflow_id: str) -> Optional[str]:
        """Execute workflow manually"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/workflows/{workflow_id}/execute",
                headers=self.headers,
                json={}
            )
            if response.status_code == 201:
                execution_id = response.json().get('data', {}).get('executionId')
                print(f"✅ Workflow execution started: {execution_id}")
                return execution_id
            else:
                print(f"❌ Failed to execute workflow: {response.status_code}")
                print(f"Response: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Error executing workflow: {str(e)}")
            return None
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict]:
        """Get execution status and results"""
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/executions/{execution_id}",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get execution status: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error getting execution status: {str(e)}")
            return None
    
    def wait_for_execution(self, execution_id: str, timeout: int = 300) -> Dict:
        """Wait for execution to complete"""
        print(f"⏳ Waiting for execution {execution_id} to complete...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_execution_status(execution_id)
            if status:
                finished = status.get('data', {}).get('finished', False)
                success = status.get('data', {}).get('success', False)
                
                if finished:
                    if success:
                        print(f"✅ Execution completed successfully")
                    else:
                        print(f"❌ Execution failed")
                    return status
            
            time.sleep(5)
        
        print(f"⏰ Execution timeout after {timeout} seconds")
        return {}

    def analyze_execution_results(self, execution_data: Dict) -> Dict:
        """Analyze execution results"""
        analysis = {
            'success': False,
            'linkedin_posted': False,
            'image_generated': False,
            'errors': [],
            'warnings': [],
            'node_results': {}
        }

        try:
            data = execution_data.get('data', {})
            analysis['success'] = data.get('success', False)

            # Analyze node results
            result_data = data.get('resultData', {})
            if result_data and 'runData' in result_data:
                run_data = result_data['runData']

                for node_name, node_data in run_data.items():
                    if node_data and len(node_data) > 0:
                        node_result = node_data[0]
                        analysis['node_results'][node_name] = {
                            'success': not node_result.get('error'),
                            'data_count': len(node_result.get('data', {}).get('main', [[]])[0]),
                            'error': node_result.get('error')
                        }

                        # Check for LinkedIn posting
                        if 'linkedin' in node_name.lower():
                            if not node_result.get('error'):
                                analysis['linkedin_posted'] = True
                            else:
                                analysis['errors'].append(f"LinkedIn node failed: {node_result.get('error')}")

                        # Check for image generation
                        if 'image' in node_name.lower() and 'generator' in node_name.lower():
                            if not node_result.get('error'):
                                analysis['image_generated'] = True
                            else:
                                analysis['errors'].append(f"Image generation failed: {node_result.get('error')}")

            return analysis

        except Exception as e:
            analysis['errors'].append(f"Analysis error: {str(e)}")
            return analysis

    def create_linkedin_fix_workflow(self) -> Dict:
        """Create the LinkedIn image fix workflow"""
        workflow = {
            "name": "LinkedIn Image Fix - Production Ready",
            "active": False,
            "nodes": [
                {
                    "parameters": {},
                    "id": "manual-trigger",
                    "name": "Manual Test Trigger",
                    "type": "n8n-nodes-base.manualTrigger",
                    "typeVersion": 1,
                    "position": [100, 100]
                },
                {
                    "parameters": {
                        "jsCode": """// LinkedIn Content & Image Generator
const generateContent = () => {
  const content = `🎓 Digital Marketing Insight: The Power of Educational Content

After working with 100+ businesses, I've learned that the most successful companies don't just sell - they educate first.

Here's what I've discovered:

✅ Educational content builds trust faster than any sales pitch
✅ People buy from experts they learn from
✅ Teaching your expertise creates lasting relationships
✅ Value-first approaches generate 3x more engagement

Real example: A local bakery started sharing baking tips instead of just posting product photos. Result? 340% increase in engagement and 67% growth in sales.

The lesson? When you help people succeed, business success follows naturally.

At GOD Digital Marketing, we specialize in education-first marketing strategies that build genuine relationships.

What's one way you could help your customers today without selling anything?

Learn more: https://godigitalmarketing.com

#DigitalMarketingEducation #BusinessGrowth #ValueFirst #GODDigitalMarketing`;

  const imagePrompt = "professional business workspace with laptop, charts, and marketing materials, modern office setting, clean design, business success theme, professional lighting, high quality, no text, no watermarks, infographic style";

  return {
    linkedinPost: content,
    imagePrompt: imagePrompt,
    timestamp: new Date().toISOString()
  };
};

return generateContent();"""
                    },
                    "id": "content-generator",
                    "name": "Content Generator",
                    "type": "n8n-nodes-base.code",
                    "typeVersion": 2,
                    "position": [300, 100]
                }
            ],
            "connections": {
                "Manual Test Trigger": {
                    "main": [
                        [
                            {
                                "node": "Content Generator",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            }
        }
        return workflow

    def add_image_generation_nodes(self, workflow: Dict) -> Dict:
        """Add image generation and LinkedIn posting nodes"""

        # Add image generator node
        image_generator = {
            "parameters": {
                "method": "POST",
                "url": "https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev",
                "sendHeaders": True,
                "headerParameters": {
                    "parameters": [
                        {
                            "name": "Authorization",
                            "value": "Bearer *************************************"
                        },
                        {
                            "name": "Content-Type",
                            "value": "application/json"
                        }
                    ]
                },
                "sendBody": True,
                "bodyParameters": {
                    "parameters": [
                        {
                            "name": "inputs",
                            "value": "={{ $json.imagePrompt }}"
                        }
                    ]
                },
                "options": {
                    "response": {
                        "response": {
                            "responseFormat": "file",
                            "outputPropertyName": "linkedin_image"
                        }
                    },
                    "timeout": 60000,
                    "retry": {
                        "enabled": True,
                        "maxAttempts": 3,
                        "waitBetween": 2000
                    }
                }
            },
            "id": "image-generator",
            "name": "LinkedIn Image Generator",
            "type": "n8n-nodes-base.httpRequest",
            "typeVersion": 4.1,
            "position": [500, 100]
        }

        # Add image validator node
        image_validator = {
            "parameters": {
                "jsCode": """// Image Validator & Fallback Creator
const validateImage = () => {
  const inputData = $input.first();
  const jsonData = inputData.json;
  const binaryData = inputData.binary;

  console.log('🔍 Validating LinkedIn image...');
  console.log('Binary keys:', Object.keys(binaryData || {}));

  // Check if image was generated successfully
  if (binaryData && binaryData.linkedin_image) {
    console.log('✅ LinkedIn image found');
    return {
      json: {
        ...jsonData,
        imageStatus: 'generated',
        imageSource: 'flux_generated',
        hasImage: true
      },
      binary: binaryData
    };
  } else {
    console.log('⚠️ No image found, creating fallback');

    // Create fallback image (1x1 transparent PNG)
    const fallbackImage = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

    return {
      json: {
        ...jsonData,
        imageStatus: 'fallback',
        imageSource: 'fallback_created',
        hasImage: true
      },
      binary: {
        linkedin_image: {
          data: Buffer.from(fallbackImage, 'base64'),
          mimeType: 'image/png',
          fileName: 'linkedin_fallback.png'
        }
      }
    };
  }
};

return validateImage();"""
            },
            "id": "image-validator",
            "name": "Image Validator",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [700, 100]
        }

        workflow["nodes"].extend([image_generator, image_validator])

        # Update connections
        workflow["connections"]["Content Generator"] = {
            "main": [
                [
                    {
                        "node": "LinkedIn Image Generator",
                        "type": "main",
                        "index": 0
                    }
                ]
            ]
        }

        workflow["connections"]["LinkedIn Image Generator"] = {
            "main": [
                [
                    {
                        "node": "Image Validator",
                        "type": "main",
                        "index": 0
                    }
                ]
            ]
        }

        return workflow

    def add_linkedin_posting_node(self, workflow: Dict) -> Dict:
        """Add LinkedIn posting node"""

        linkedin_node = {
            "parameters": {
                "person": "Kn0HZYImT9",  # You'll need to update this with your LinkedIn person ID
                "text": "={{ $json.linkedinPost }}",
                "shareMediaCategory": "={{ $json.hasImage ? 'IMAGE' : 'NONE' }}",
                "binaryPropertyName": "linkedin_image",
                "additionalFields": {
                    "visibility": "PUBLIC"
                }
            },
            "id": "linkedin-poster",
            "name": "LinkedIn Poster",
            "type": "n8n-nodes-base.linkedIn",
            "typeVersion": 1,
            "position": [900, 100],
            "credentials": {
                "linkedInOAuth2Api": {
                    "id": "your-linkedin-credential-id",  # You'll need to update this
                    "name": "LinkedIn account"
                }
            }
        }

        # Add final validator
        final_validator = {
            "parameters": {
                "jsCode": """// Final Results Validator
const validateResults = () => {
  const inputData = $input.first();
  const jsonData = inputData.json;

  console.log('📊 Final Results Analysis:');
  console.log('Image Status:', jsonData.imageStatus);
  console.log('Image Source:', jsonData.imageSource);
  console.log('Has Image:', jsonData.hasImage);

  const results = {
    success: true,
    linkedinPosted: true,
    imageGenerated: jsonData.imageStatus === 'generated',
    imageFallback: jsonData.imageStatus === 'fallback',
    timestamp: new Date().toISOString(),
    summary: `LinkedIn post completed with ${jsonData.imageSource} image`
  };

  console.log('✅ Workflow completed successfully');
  console.log('Results:', JSON.stringify(results, null, 2));

  return results;
};

return validateResults();"""
            },
            "id": "final-validator",
            "name": "Final Validator",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [1100, 100]
        }

        workflow["nodes"].extend([linkedin_node, final_validator])

        # Update connections
        workflow["connections"]["Image Validator"] = {
            "main": [
                [
                    {
                        "node": "LinkedIn Poster",
                        "type": "main",
                        "index": 0
                    }
                ]
            ]
        }

        workflow["connections"]["LinkedIn Poster"] = {
            "main": [
                [
                    {
                        "node": "Final Validator",
                        "type": "main",
                        "index": 0
                    }
                ]
            ]
        }

        return workflow

    def deploy_complete_workflow(self) -> Optional[str]:
        """Deploy the complete LinkedIn fix workflow"""
        print("🚀 Creating complete LinkedIn image fix workflow...")

        # Create base workflow
        workflow = self.create_linkedin_fix_workflow()

        # Add image generation
        workflow = self.add_image_generation_nodes(workflow)

        # Add LinkedIn posting
        workflow = self.add_linkedin_posting_node(workflow)

        # Check if workflow already exists
        existing = self.find_workflow_by_name(workflow["name"])

        if existing:
            print(f"📝 Updating existing workflow: {existing['id']}")
            success = self.update_workflow(existing['id'], workflow)
            if success:
                self.activate_workflow(existing['id'])
                return existing['id']
        else:
            print("📝 Creating new workflow...")
            # Create new workflow
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/workflows",
                    headers=self.headers,
                    json=workflow
                )
                if response.status_code == 201:
                    workflow_id = response.json()['data']['id']
                    print(f"✅ Workflow created: {workflow_id}")
                    self.activate_workflow(workflow_id)
                    return workflow_id
                else:
                    print(f"❌ Failed to create workflow: {response.status_code}")
                    print(f"Response: {response.text}")
                    return None
            except Exception as e:
                print(f"❌ Error creating workflow: {str(e)}")
                return None

        return None

    def run_complete_test(self) -> Dict:
        """Run complete workflow test and analysis"""
        print("🧪 Starting complete workflow test...")

        # Deploy workflow
        workflow_id = self.deploy_complete_workflow()
        if not workflow_id:
            return {"success": False, "error": "Failed to deploy workflow"}

        print(f"⏳ Waiting 5 seconds for workflow to be ready...")
        time.sleep(5)

        # Execute workflow
        execution_id = self.execute_workflow(workflow_id)
        if not execution_id:
            return {"success": False, "error": "Failed to execute workflow"}

        # Wait for completion
        execution_result = self.wait_for_execution(execution_id, timeout=180)
        if not execution_result:
            return {"success": False, "error": "Execution timeout"}

        # Analyze results
        analysis = self.analyze_execution_results(execution_result)

        # Print detailed report
        self.print_test_report(analysis, execution_result)

        return {
            "success": True,
            "workflow_id": workflow_id,
            "execution_id": execution_id,
            "analysis": analysis,
            "execution_result": execution_result
        }

    def print_test_report(self, analysis: Dict, execution_result: Dict):
        """Print detailed test report"""
        print("\n" + "="*60)
        print("📊 LINKEDIN IMAGE FIX - TEST REPORT")
        print("="*60)

        print(f"🎯 Overall Success: {'✅ YES' if analysis['success'] else '❌ NO'}")
        print(f"📱 LinkedIn Posted: {'✅ YES' if analysis['linkedin_posted'] else '❌ NO'}")
        print(f"🖼️ Image Generated: {'✅ YES' if analysis['image_generated'] else '❌ NO'}")

        if analysis['errors']:
            print(f"\n❌ Errors Found:")
            for error in analysis['errors']:
                print(f"   • {error}")

        if analysis['warnings']:
            print(f"\n⚠️ Warnings:")
            for warning in analysis['warnings']:
                print(f"   • {warning}")

        print(f"\n📋 Node Results:")
        for node_name, result in analysis['node_results'].items():
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {node_name}: {result['data_count']} items")
            if result['error']:
                print(f"      Error: {result['error']}")

        # Execution details
        data = execution_result.get('data', {})
        print(f"\n⏱️ Execution Details:")
        print(f"   • Started: {data.get('startedAt', 'Unknown')}")
        print(f"   • Finished: {data.get('stoppedAt', 'Unknown')}")
        print(f"   • Mode: {data.get('mode', 'Unknown')}")
        print(f"   • Status: {data.get('status', 'Unknown')}")

        print("\n" + "="*60)

        if analysis['success'] and analysis['linkedin_posted']:
            print("🎉 SUCCESS: LinkedIn image posting is working correctly!")
        else:
            print("⚠️ ISSUES DETECTED: Review the errors above")

        print("="*60)


def main():
    """Main execution function"""
    print("🚀 N8N LinkedIn Image Fix - Automated Deployment & Testing")
    print("="*60)

    # Configuration
    API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwZWFlOTJmYy05M2RkLTRjMTQtOTRmMC1lODdmY2IzN2MyYmYiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQ2NTk5MjQxfQ.jphn46ViMNya7xlk4xrZWY95ad2Bb5P0qnXmLpotBO4"
    BASE_URL = "http://localhost:5678"  # Update if your n8n is on different port/host

    # Initialize manager
    manager = N8NWorkflowManager(API_KEY, BASE_URL)

    # Test connection
    if not manager.test_connection():
        print("❌ Cannot connect to N8N. Please check:")
        print("   • N8N is running")
        print("   • API key is correct")
        print("   • Base URL is correct")
        sys.exit(1)

    # Run complete test
    try:
        result = manager.run_complete_test()

        if result["success"]:
            print(f"\n✅ Test completed successfully!")
            print(f"📋 Workflow ID: {result['workflow_id']}")
            print(f"🔄 Execution ID: {result['execution_id']}")
        else:
            print(f"\n❌ Test failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
