@echo off
echo ========================================
echo N8N LinkedIn Image Fix - Auto Deploy
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

echo Python found! Starting deployment...
echo.

python setup_and_run.py

echo.
echo ========================================
echo Deployment completed!
echo ========================================
pause
