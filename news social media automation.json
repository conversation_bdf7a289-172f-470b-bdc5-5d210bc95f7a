{"meta": {"instanceId": "a4b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "b8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2000, 300]}, {"parameters": {"jsCode": "// NEWS FETCHER WITH ENHANCED FILTERING\n// Fetches latest news from multiple sources with advanced filtering\n\nconst newsApiKey = '********************************';\nconst sources = [\n  'techcrunch',\n  'the-verge',\n  'wired',\n  'ars-technica',\n  'engadget',\n  'mashable',\n  'recode',\n  'venturebeat'\n];\n\nconst keywords = [\n  'AI',\n  'artificial intelligence',\n  'machine learning',\n  'automation',\n  'digital marketing',\n  'social media',\n  'SEO',\n  'Google algorithm',\n  'content marketing',\n  'business automation',\n  'productivity tools',\n  'marketing technology',\n  'SaaS',\n  'startup',\n  'entrepreneurship'\n];\n\ntry {\n  const newsData = [];\n  \n  // Fetch from multiple sources\n  for (const source of sources.slice(0, 3)) { // Limit to 3 sources to avoid rate limits\n    try {\n      const response = await fetch(`https://newsapi.org/v2/top-headlines?sources=${source}&apiKey=${newsApiKey}`);\n      const data = await response.json();\n      \n      if (data.articles) {\n        const filteredArticles = data.articles.filter(article => {\n          const content = `${article.title} ${article.description || ''}`.toLowerCase();\n          return keywords.some(keyword => content.includes(keyword.toLowerCase()));\n        }).slice(0, 5); // Top 5 from each source\n        \n        newsData.push(...filteredArticles);\n      }\n    } catch (error) {\n      console.log(`Error fetching from ${source}:`, error.message);\n    }\n  }\n  \n  // Sort by publication date and remove duplicates\n  const uniqueNews = newsData\n    .filter((article, index, self) => \n      index === self.findIndex(a => a.title === article.title)\n    )\n    .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))\n    .slice(0, 10); // Top 10 most recent unique articles\n  \n  console.log(`Fetched ${uniqueNews.length} relevant news articles`);\n  \n  return {\n    newsArticles: uniqueNews,\n    fetchedAt: new Date().toISOString(),\n    totalSources: sources.length,\n    keywords: keywords\n  };\n  \n} catch (error) {\n  console.error('News fetching error:', error.message);\n  return {\n    newsArticles: [],\n    error: error.message,\n    fetchedAt: new Date().toISOString()\n  };\n}"}, "id": "c8c8c8c8-c8c8-c8c8-c8c8-c8c8c8c8c8c8", "name": "News Fetcher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1800, 300], "notes": "Fetches latest news from multiple tech sources with keyword filtering for AI, automation, marketing, and business topics"}, {"parameters": {"jsCode": "// CONTENT GENERATOR WITH NEWS INTEGRATION\n// Generates platform-specific content based on latest news and trends\n\nconst contentGenerator = () => {\n  try {\n    const newsData = $input.first().json;\n    const articles = newsData.newsArticles || [];\n    \n    console.log('🚀 Generating content based on', articles.length, 'news articles');\n    \n    // Select top 3 most relevant articles\n    const topArticles = articles.slice(0, 3);\n    \n    // Generate content themes based on news\n    const themes = extractThemes(topArticles);\n    \n    // Generate platform-specific content\n    const platforms = {\n      linkedin: generateLinkedInPost(topArticles, themes),\n      twitter: generateTwitterPost(topArticles, themes),\n      instagram: generateInstagramPost(topArticles, themes),\n      facebook: generateFacebookPost(topArticles, themes),\n      reddit: generateRedditPost(topArticles, themes)\n    };\n    \n    function extractThemes(articles) {\n      const allText = articles.map(a => `${a.title} ${a.description || ''}`).join(' ');\n      \n      const themes = {\n        ai: /AI|artificial intelligence|machine learning|automation/gi.test(allText),\n        marketing: /marketing|SEO|social media|content|brand/gi.test(allText),\n        business: /business|startup|entrepreneur|SaaS|productivity/gi.test(allText),\n        technology: /tech|software|app|platform|digital/gi.test(allText)\n      };\n      \n      return themes;\n    }\n    \n    function generateLinkedInPost(articles, themes) {\n      const article = articles[0];\n      if (!article) return null;\n      \n      const insights = [\n        \"Here's what this means for businesses:\",\n        \"Key takeaways for professionals:\",\n        \"This trend is reshaping how we work:\",\n        \"What industry leaders need to know:\"\n      ];\n      \n      const content = `${insights[Math.floor(Math.random() * insights.length)]}\n\n📈 ${article.title}\n\n${article.description || ''}\n\nMy thoughts: This development highlights the importance of staying ahead of technological trends. Companies that adapt quickly will have a significant competitive advantage.\n\nWhat's your take on this? How do you see this impacting your industry?\n\n#Innovation #Technology #Business #AI #Automation`;\n      \n      return {\n        content: content,\n        hashtags: '#Innovation #Technology #Business #AI #Automation',\n        source: article.url,\n        title: article.title\n      };\n    }\n    \n    function generateTwitterPost(articles, themes) {\n      const article = articles[0];\n      if (!article) return null;\n      \n      const hooks = [\n        \"🚨 Breaking:\",\n        \"🔥 Hot take:\",\n        \"💡 Insight:\",\n        \"⚡ Quick update:\"\n      ];\n      \n      const content = `${hooks[Math.floor(Math.random() * hooks.length)]} ${article.title}\n\nThis could change everything for businesses using AI and automation.\n\nThoughts? 🤔\n\n#AI #Tech #Innovation #Business`;\n      \n      return {\n        content: content.substring(0, 280),\n        hashtags: '#AI #Tech #Innovation #Business',\n        source: article.url,\n        title: article.title\n      };\n    }\n    \n    function generateInstagramPost(articles, themes) {\n      const article = articles[0];\n      if (!article) return null;\n      \n      const content = `✨ TECH UPDATE ✨\n\n${article.title}\n\n${article.description || ''}\n\nThis is exactly why I'm passionate about staying on top of tech trends! 🚀\n\nThe future of business is being written right now, and those who adapt fastest will win.\n\nWhat do you think about this development? Drop your thoughts below! 👇\n\n#TechNews #Innovation #AI #Business #Entrepreneur #DigitalTransformation #FutureOfWork #TechTrends #Automation #Success`;\n      \n      return {\n        content: content,\n        hashtags: '#TechNews #Innovation #AI #Business #Entrepreneur #DigitalTransformation #FutureOfWork #TechTrends #Automation #Success',\n        source: article.url,\n        title: article.title\n      };\n    }\n    \n    function generateFacebookPost(articles, themes) {\n      const article = articles[0];\n      if (!article) return null;\n      \n      const content = `Interesting development in the tech world! 🌟\n\n${article.title}\n\n${article.description || ''}\n\nI've been following this trend closely, and it's fascinating to see how quickly technology is evolving. This could have major implications for businesses and professionals across all industries.\n\nWhat are your thoughts? Have you noticed similar changes in your field?\n\nLet's discuss in the comments! 💬\n\n#Technology #Innovation #Business #AI #DigitalTransformation`;\n      \n      return {\n        content: content,\n        hashtags: '#Technology #Innovation #Business #AI #DigitalTransformation',\n        source: article.url,\n        title: article.title\n      };\n    }\n    \n    function generateRedditPost(articles, themes) {\n      const article = articles[0];\n      if (!article) return null;\n      \n      const content = `Thought this community might find this interesting:\n\n${article.description || ''}\n\nI've been tracking developments in this space, and this seems like a significant step forward. The implications for businesses and automation are pretty substantial.\n\nWhat do you all think? Anyone else following this trend?\n\nSource: ${article.url}`;\n      \n      return {\n        content: content,\n        title: article.title,\n        source: article.url,\n        subreddit: themes.ai ? 'artificial' : themes.business ? 'entrepreneur' : 'technology'\n      };\n    }\n    \n    console.log('✅ Content generation completed for all platforms');\n    \n    return {\n      platforms: platforms,\n      sourceArticles: topArticles,\n      themes: themes,\n      generatedAt: new Date().toISOString(),\n      contentReady: true\n    };\n    \n  } catch (error) {\n    console.error('Content generation error:', error.message);\n    return {\n      platforms: {},\n      error: error.message,\n      generatedAt: new Date().toISOString()\n    };\n  }\n};\n\nreturn contentGenerator();"}, "id": "d8d8d8d8-d8d8-d8d8-d8d8-d8d8d8d8d8d8", "name": "Content Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1600, 300], "notes": "Generates platform-specific content based on latest news articles with human-like writing style and engagement hooks"}, {"parameters": {"jsCode": "// HUMAN ENHANCEMENT ENGINE\n// Adds human-like characteristics to AI-generated content\n\nconst humanEnhancer = () => {\n  try {\n    const contentData = $input.first().json;\n    const platforms = contentData.platforms || {};\n    \n    console.log('🧠 Enhancing content with human characteristics...');\n    \n    const enhancedPlatforms = {};\n    \n    Object.keys(platforms).forEach(platform => {\n      const originalContent = platforms[platform];\n      if (originalContent && originalContent.content) {\n        enhancedPlatforms[platform] = enhanceContent(originalContent, platform);\n      }\n    });\n    \n    function enhanceContent(content, platform) {\n      let enhanced = content.content;\n      \n      // Add personal touches\n      const personalTouches = [\n        \"Honestly, \",\n        \"I've been thinking about this, and \",\n        \"From my experience, \",\n        \"What I find fascinating is \",\n        \"Here's what caught my attention: \"\n      ];\n      \n      // Add conversational elements\n      const conversationalElements = [\n        \"you know what?\",\n        \"here's the thing\",\n        \"let's be real\",\n        \"honestly speaking\",\n        \"if I'm being honest\"\n      ];\n      \n      // Add emotional expressions\n      const emotions = [\n        \"I'm excited about\",\n        \"This genuinely surprised me\",\n        \"I'm honestly amazed by\",\n        \"This got me thinking\",\n        \"I can't help but wonder\"\n      ];\n      \n      // Platform-specific enhancements\n      switch(platform) {\n        case 'linkedin':\n          enhanced = addLinkedInHumanTouch(enhanced);\n          break;\n        case 'twitter':\n          enhanced = addTwitterHumanTouch(enhanced);\n          break;\n        case 'instagram':\n          enhanced = addInstagramHumanTouch(enhanced);\n          break;\n        case 'facebook':\n          enhanced = addFacebookHumanTouch(enhanced);\n          break;\n        case 'reddit':\n          enhanced = addRedditHumanTouch(enhanced);\n          break;\n      }\n      \n      return {\n        ...content,\n        content: enhanced,\n        humanEnhanced: true,\n        enhancedAt: new Date().toISOString()\n      };\n    }\n    \n    function addLinkedInHumanTouch(content) {\n      // Add professional but personal tone\n      const personalStarters = [\n        \"I've been following this trend closely, and \",\n        \"As someone who works in this space, \",\n        \"From my perspective, \",\n        \"What strikes me about this is \"\n      ];\n      \n      const starter = personalStarters[Math.floor(Math.random() * personalStarters.length)];\n      \n      // Add contractions and casual language\n      content = content.replace(/I am /g, \"I'm \");\n      content = content.replace(/do not /g, \"don't \");\n      content = content.replace(/cannot /g, \"can't \");\n      content = content.replace(/will not /g, \"won't \");\n      \n      return content;\n    }\n    \n    function addTwitterHumanTouch(content) {\n      // Add casual, conversational tone\n      content = content.replace(/\\. /g, '. ');\n      \n      // Add thinking emoji or casual expressions\n      if (!content.includes('🤔') && Math.random() > 0.5) {\n        content = content.replace(/Thoughts\\?/, 'Thoughts? 🤔');\n      }\n      \n      return content;\n    }\n    \n    function addInstagramHumanTouch(content) {\n      // Add personal story elements\n      const personalElements = [\n        \"This reminds me of when I first started learning about tech...\",\n        \"I remember being skeptical about AI at first, but now...\",\n        \"As someone who's passionate about innovation...\"\n      ];\n      \n      return content;\n    }\n    \n    function addFacebookHumanTouch(content) {\n      // Add community-focused language\n      content = content.replace(/What are your thoughts\\?/, \"What do you all think? I'd love to hear your perspectives!\");\n      \n      return content;\n    }\n    \n    function addRedditHumanTouch(content) {\n      // Add Reddit-specific casual tone\n      const redditPhrases = [\n        \"TL;DR: \",\n        \"Edit: \",\n        \"Update: \",\n        \"IMHO, \"\n      ];\n      \n      return content;\n    }\n    \n    console.log('✅ Human enhancement completed');\n    \n    return {\n      ...contentData,\n      platforms: enhancedPlatforms,\n      humanEnhancement: {\n        enhanced: true,\n        enhancedAt: new Date().toISOString(),\n        platformsEnhanced: Object.keys(enhancedPlatforms).length\n      }\n    };\n    \n  } catch (error) {\n    console.error('Human enhancement error:', error.message);\n    return {\n      ...contentData,\n      humanEnhancement: {\n        enhanced: false,\n        error: error.message,\n        enhancedAt: new Date().toISOString()\n      }\n    };\n  }\n};\n\nreturn humanEnhancer();"}, "id": "e8e8e8e8-e8e8-e8e8-e8e8-e8e8e8e8e8e8", "name": "Human Enhancement Engine", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1400, 300], "notes": "Adds human-like characteristics, personal touches, and conversational elements to make content feel more authentic and engaging"}, {"parameters": {"jsCode": "// VISUAL PROMPT GENERATOR\n// Generates AI image prompts based on content and news themes\n\nconst visualGenerator = () => {\n  try {\n    const contentData = $input.first().json;\n    const platforms = contentData.platforms || {};\n    const articles = contentData.sourceArticles || [];\n    \n    console.log('🎨 Generating visual prompts for content...');\n    \n    const visualPrompts = {};\n    \n    Object.keys(platforms).forEach(platform => {\n      const content = platforms[platform];\n      if (content && content.content) {\n        visualPrompts[platform] = generatePrompt(content, platform, articles);\n      }\n    });\n    \n    function generatePrompt(content, platform, articles) {\n      const themes = extractVisualThemes(content.content);\n      const platformSpecs = getPlatformSpecs(platform);\n      \n      const basePrompt = `professional business infographic design, ${themes.style}, ${platformSpecs.dimensions}, modern flat design, clean typography, ${themes.elements}, professional color scheme, high quality, no text content, no watermarks, marketing infographic style`;\n      \n      return {\n        prompt: basePrompt,\n        themes: themes,\n        platform: platform,\n        style: themes.style\n      };\n    }\n    \n    function extractVisualThemes(content) {\n      const text = content.toLowerCase();\n      \n      if (text.includes('ai') || text.includes('artificial intelligence') || text.includes('automation')) {\n        return {\n          style: 'AI and technology infographic with neural networks, circuit patterns, futuristic elements',\n          elements: 'tech icons, data visualization, AI symbols, automation graphics',\n          colorScheme: 'blue and purple gradient, modern tech colors'\n        };\n      }\n      \n      if (text.includes('marketing') || text.includes('social media') || text.includes('seo')) {\n        return {\n          style: 'digital marketing infographic with growth charts, social media icons',\n          elements: 'marketing metrics, social media symbols, growth arrows, engagement icons',\n          colorScheme: 'vibrant marketing colors, orange and blue accents'\n        };\n      }\n      \n      if (text.includes('business') || text.includes('startup') || text.includes('entrepreneur')) {\n        return {\n          style: 'business strategy infographic with professional charts and graphs',\n          elements: 'business icons, strategy diagrams, success metrics, professional graphics',\n          colorScheme: 'corporate blue and green, professional color palette'\n        };\n      }\n      \n      // Default tech theme\n      return {\n        style: 'modern technology infographic with clean design elements',\n        elements: 'tech icons, innovation symbols, digital transformation graphics',\n        colorScheme: 'modern blue gradient, clean professional colors'\n      };\n    }\n    \n    function getPlatformSpecs(platform) {\n      const specs = {\n        linkedin: {\n          dimensions: '1200x630 aspect ratio',\n          style: 'professional corporate design'\n        },\n        instagram: {\n          dimensions: '1080x1080 square format',\n          style: 'visually striking social media design'\n        },\n        twitter: {\n          dimensions: '1200x675 aspect ratio',\n          style: 'concise attention-grabbing design'\n        },\n        facebook: {\n          dimensions: '1200x630 aspect ratio',\n          style: 'community-friendly accessible design'\n        },\n        reddit: {\n          dimensions: '1200x630 aspect ratio',\n          style: 'informative discussion-focused design'\n        }\n      };\n      \n      return specs[platform] || specs.linkedin;\n    }\n    \n    console.log('✅ Visual prompt generation completed');\n    \n    return {\n      ...contentData,\n      visualPrompts: {\n        aiImagePrompts: visualPrompts,\n        generatedAt: new Date().toISOString(),\n        totalPrompts: Object.keys(visualPrompts).length\n      }\n    };\n    \n  } catch (error) {\n    console.error('Visual prompt generation error:', error.message);\n    return {\n      ...contentData,\n      visualPrompts: {\n        aiImagePrompts: {},\n        error: error.message,\n        generatedAt: new Date().toISOString()\n      }\n    };\n  }\n};\n\nreturn visualGenerator();"}, "id": "f8f8f8f8-f8f8-f8f8-f8f8-f8f8f8f8f8f8", "name": "Visual Prompt Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 300], "notes": "Generates AI image prompts based on content themes and platform requirements for creating relevant infographics"}, {"parameters": {"jsCode": "// FINAL QUALITY VALIDATOR\n// Validates content quality before publishing\n\nconst qualityValidator = () => {\n  try {\n    const contentData = $input.first().json;\n    \n    console.log('🔍 Final Quality Validation: Ensuring all content meets production standards...');\n    \n    const validation = {\n      contentReadiness: validateContentReadiness(contentData),\n      humanAuthenticity: validateHumanAuthenticity(contentData),\n      platformCompliance: validatePlatformCompliance(contentData)\n    };\n    \n    function validateContentReadiness(data) {\n      const issues = [];\n      const platforms = data.platforms || {};\n      \n      Object.keys(platforms).forEach(platform => {\n        const content = platforms[platform]?.content;\n        if (!content) {\n          issues.push(`${platform}: Missing content`);\n          return;\n        }\n        \n        // Length checks per platform\n        const lengthLimits = {\n          twitter: { min: 50, max: 280 },\n          linkedin: { min: 100, max: 3000 },\n          instagram: { min: 50, max: 2200 },\n          facebook: { min: 50, max: 63206 },\n          reddit: { min: 100, max: 40000 }\n        };\n        \n        const limits = lengthLimits[platform];\n        if (limits) {\n          if (content.length < limits.min) {\n            issues.push(`${platform}: Content too short (${content.length}/${limits.min})`);\n          }\n          if (content.length > limits.max) {\n            issues.push(`${platform}: Content too long (${content.length}/${limits.max})`);\n          }\n        }\n        \n        // Value delivery check\n        if (!/tip|strategy|help|learn|guide|insight|solution|advice/gi.test(content)) {\n          issues.push(`${platform}: Content lacks clear value proposition`);\n        }\n      });\n      \n      return { valid: issues.length === 0, issues, score: Math.max(0, 100 - (issues.length * 10)) };\n    }\n    \n    function validateHumanAuthenticity(data) {\n      const issues = [];\n      const platforms = data.platforms || {};\n      \n      Object.keys(platforms).forEach(platform => {\n        const content = platforms[platform]?.content || '';\n        \n        // AI detection patterns\n        const aiPatterns = [\n          /I'd be happy to|I'm pleased to|I should mention|It's worth noting/gi,\n          /Let me break this down|Here's the thing|Feel free to|I hope this helps/gi,\n          /As an AI|delighted to|I'd love to help|happy to assist/gi\n        ];\n        \n        aiPatterns.forEach((pattern, index) => {\n          if (pattern.test(content)) {\n            issues.push(`${platform}: Contains AI pattern #${index + 1}`);\n          }\n        });\n      });\n      \n      return { valid: issues.length === 0, issues, score: Math.max(0, 100 - (issues.length * 8)) };\n    }\n    \n    function validatePlatformCompliance(data) {\n      const issues = [];\n      const platforms = data.platforms || {};\n      \n      // Required platforms check\n      const requiredPlatforms = ['linkedin', 'twitter', 'instagram', 'facebook'];\n      requiredPlatforms.forEach(platform => {\n        if (!platforms[platform] || !platforms[platform].content) {\n          issues.push(`Missing required platform: ${platform}`);\n        }\n      });\n      \n      return { valid: issues.length === 0, issues, score: Math.max(0, 100 - (issues.length * 12)) };\n    }\n    \n    // Calculate overall quality score\n    const overallScore = Math.round(\n      (validation.contentReadiness.score * 0.4) +\n      (validation.humanAuthenticity.score * 0.3) +\n      (validation.platformCompliance.score * 0.3)\n    );\n    \n    const allValid = Object.values(validation).every(v => v.valid);\n    const totalIssues = Object.values(validation).reduce((sum, v) => sum + v.issues.length, 0);\n    \n    const qualityReport = {\n      timestamp: new Date().toISOString(),\n      overallStatus: allValid ? 'READY_FOR_PUBLISHING' : 'NEEDS_ATTENTION',\n      overallScore: overallScore,\n      qualityGrade: overallScore >= 95 ? 'A+' : \n                   overallScore >= 90 ? 'A' : \n                   overallScore >= 85 ? 'B+' : \n                   overallScore >= 80 ? 'B' : 'C',\n      validation: validation,\n      totalIssues: totalIssues,\n      readyForPublishing: allValid && overallScore >= 85\n    };\n    \n    console.log('✅ Final Quality Validation completed');\n    console.log('Overall Status:', qualityReport.overallStatus);\n    console.log('Quality Grade:', qualityReport.qualityGrade);\n    console.log('Overall Score:', qualityReport.overallScore);\n    console.log('Ready for Publishing:', qualityReport.readyForPublishing);\n    \n    return {\n      ...contentData,\n      finalQualityReport: qualityReport,\n      readyForPublishing: qualityReport.readyForPublishing\n    };\n    \n  } catch (error) {\n    console.error('Final quality validation error:', error.message);\n    return {\n      ...contentData,\n      finalQualityReport: {\n        overallStatus: 'ERROR',\n        error: error.message,\n        readyForPublishing: false,\n        timestamp: new Date().toISOString()\n      }\n    };\n  }\n};\n\nreturn qualityValidator();"}, "id": "g8g8g8g8-g8g8-g8g8-g8g8-g8g8g8g8g8g8", "name": "Final Quality Validator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 300], "notes": "Final quality assurance before posting - validates content readiness, human authenticity, and platform compliance"}], "connections": {"Schedule Trigger": {"main": [[{"node": "News Fetcher", "type": "main", "index": 0}]]}, "News Fetcher": {"main": [[{"node": "Content Generator", "type": "main", "index": 0}]]}, "Content Generator": {"main": [[{"node": "Human Enhancement Engine", "type": "main", "index": 0}]]}, "Human Enhancement Engine": {"main": [[{"node": "Visual Prompt Generator", "type": "main", "index": 0}]]}, "Visual Prompt Generator": {"main": [[{"node": "Final Quality Validator", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-02T12:00:00.000Z", "versionId": "1"}